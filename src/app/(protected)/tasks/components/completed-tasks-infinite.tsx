"use client";

import { useEffect, useRef } from "react";
import { Task, TaskSortOption } from "@/lib/db";
import { useCompletedTasksInfiniteQuery } from "@/lib/queries";
import { TaskList } from "./task-list";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

interface CompletedTasksInfiniteProps {
  listId: string;
  sortOption: TaskSortOption;
  userId?: string;
  enabled: boolean; // Only load when completed section is expanded
  allTasks?: Task[]; // All tasks including subtasks for TaskItem to find its subtasks
  onTaskUpdated: (updatedTask?: Task, statusChanged?: boolean) => void;
  onTaskDeleted: (deletedTaskId?: string) => void;
  onTasksReordered: (tasks: Task[]) => void;
  listColor?: string | null;
  taskCounts?: Record<string, number>;
  taskMode?: "completion" | "selection";
  selectedTaskIds?: Set<string>;
  onTaskSelectionChange?: (selectedIds: Set<string>) => void;
  lastSelectedTaskId?: string | null;
  isInlineEditEnabled?: boolean;
  activeActionIconsTaskId?: string | null;
  onActionIconsChange?: (taskId: string | null) => void;
  onNavigateToTask?: (taskId: string) => void;
  isTagFiltered?: boolean;
  currentSpaceId?: string;
}

export function CompletedTasksInfinite({
  listId,
  sortOption,
  userId,
  enabled,
  allTasks,
  onTaskUpdated,
  onTaskDeleted,
  onTasksReordered,
  listColor,
  taskCounts,
  taskMode = "completion",
  selectedTaskIds = new Set(),
  onTaskSelectionChange,
  lastSelectedTaskId = null,
  isInlineEditEnabled = true,
  activeActionIconsTaskId = null,
  onActionIconsChange,
  onNavigateToTask,
  isTagFiltered = false,
  currentSpaceId,
}: CompletedTasksInfiniteProps) {
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
  } = useCompletedTasksInfiniteQuery(listId, sortOption, userId, enabled);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    if (!enabled || !hasNextPage || isFetchingNextPage) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting) {
          fetchNextPage();
        }
      },
      {
        threshold: 0.1,
        rootMargin: "100px", // Start loading 100px before the element is visible
      }
    );

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [enabled, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Don't render anything if not enabled (section collapsed)
  if (!enabled) {
    return null;
  }

  // Loading state for initial load
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        <span className="ml-2 text-sm text-muted-foreground">Loading completed tasks...</span>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <p className="text-sm text-muted-foreground mb-2">
            Failed to load completed tasks
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchNextPage()}
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // Flatten all pages into a single array of tasks
  const allCompletedTasks = data?.pages.flatMap(page => page.tasks) || [];

  // No completed tasks
  if (allCompletedTasks.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <p className="text-sm text-muted-foreground">No completed tasks</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {/* Render completed tasks */}
      <TaskList
        tasks={allCompletedTasks}
        allTasks={allTasks}
        onTaskUpdated={onTaskUpdated}
        onTaskDeleted={onTaskDeleted}
        onTasksReordered={onTasksReordered}
        sortOption={sortOption}
        listId={listId}
        listColor={listColor}
        taskCounts={taskCounts}
        taskMode={taskMode}
        selectedTaskIds={selectedTaskIds}
        onTaskSelectionChange={onTaskSelectionChange}
        lastSelectedTaskId={lastSelectedTaskId}
        isInlineEditEnabled={isInlineEditEnabled}
        activeActionIconsTaskId={activeActionIconsTaskId}
        onActionIconsChange={onActionIconsChange}
        onNavigateToTask={onNavigateToTask}
        isTagFiltered={isTagFiltered}
        currentSpaceId={currentSpaceId}
      />

      {/* Load more trigger and loading indicator */}
      <div ref={loadMoreRef} className="py-4">
        {isFetchingNextPage && (
          <div className="flex items-center justify-center">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
          </div>
        )}
        
        {!hasNextPage && allCompletedTasks.length > 0 && (
          <div className="flex items-center justify-center">
            <p className="text-xs text-muted-foreground">All completed tasks loaded</p>
          </div>
        )}
      </div>
    </div>
  );
}
